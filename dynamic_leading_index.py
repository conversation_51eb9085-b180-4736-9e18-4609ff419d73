# 10年国债期货（T）周度宏观领先指数合成代码
#
# 作者: Gemini (Based on user's final, advanced logic)
# 日期: 2025-07-10
#
# 核心逻辑:
# 1. 为13个宏观因子中的每一个，通过交叉相关性分析找到其各自的最佳领先期。
# 2. 基于最佳领先期，构建一个时间对齐的"预测因子矩阵"。
# 3. 基于"预测因子矩阵"与未来收益率的相关性，数据驱动地计算各因子权重。
# 4. 合成最终的、最大化领先性的"动态领先期加权指数"。
# ==============================================================================

# --- 第一步：环境准备与数据加载 ---

# 导入必要的Python库
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os # 导入os库以处理文件路径
from scipy.stats import spearmanr # 导入斯皮尔曼相关系数计算工具
import warnings
warnings.filterwarnings('ignore') # 忽略一些不影响结果的警告

# 设置matplotlib以支持中文显示
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# --- 定义输入与输出路径 ---
# 输入文件路径
file_path = '/Users/<USER>/Desktop/国债期货量化因子.xlsx'
factor_sheet_name = '周频因子'
futures_sheet_name = 'T主连行情'

# 输出文件夹路径（请确保该文件夹已创建）
output_dir = '/Users/<USER>/Desktop/领先因子/'
os.makedirs(output_dir, exist_ok=True) # 如果文件夹不存在，则自动创建

# --- 数据加载与准备 ---
try:
    # 加载宏观因子数据
    df_factors = pd.read_excel(file_path, sheet_name=factor_sheet_name)
    print(f"原始因子数据形状：{df_factors.shape}")

    # 处理第一列作为日期索引，跳过无法解析的行
    first_col = df_factors.columns[0]
    valid_dates = []
    valid_indices = []

    for i, value in enumerate(df_factors[first_col]):
        try:
            date_val = pd.to_datetime(value)
            valid_dates.append(date_val)
            valid_indices.append(i)
        except:
            print(f'跳过第{i+1}行，无法解析的值：{value}')
            continue

    # 只保留有效日期的行
    df_factors = df_factors.iloc[valid_indices].copy()
    df_factors.index = valid_dates
    df_factors = df_factors.drop(columns=[first_col])
    df_factors = df_factors.sort_index()
    # 使用更安全的日期筛选方式
    start_date = pd.to_datetime('2019-01-05')
    end_date = pd.to_datetime('2025-07-05')
    df_factors = df_factors[(df_factors.index >= start_date) & (df_factors.index <= end_date)]

    # 加载国债期货行情数据
    df_t_futures = pd.read_excel(file_path, sheet_name=futures_sheet_name)
    print(f"原始期货数据形状：{df_t_futures.shape}")

    # 处理期货数据的日期索引
    first_col_futures = df_t_futures.columns[0]
    valid_dates_futures = []
    valid_indices_futures = []

    for i, value in enumerate(df_t_futures[first_col_futures]):
        try:
            date_val = pd.to_datetime(value)
            valid_dates_futures.append(date_val)
            valid_indices_futures.append(i)
        except:
            print(f'跳过期货数据第{i+1}行，无法解析的值：{value}')
            continue

    # 只保留有效日期的行
    df_t_futures = df_t_futures.iloc[valid_indices_futures].copy()
    df_t_futures.index = valid_dates_futures
    df_t_futures = df_t_futures.drop(columns=[first_col_futures])
    df_t_futures = df_t_futures.sort_index()
    # 使用更安全的日期筛选方式
    start_date_futures = pd.to_datetime('2019-01-02')
    end_date_futures = pd.to_datetime('2025-07-11')
    df_t_futures = df_t_futures[(df_t_futures.index >= start_date_futures) & (df_t_futures.index <= end_date_futures)]

    print("宏观因子与国债期货行情数据加载成功！")
    print(f"因子数据最终形状：{df_factors.shape}")
    print(f"期货数据最终形状：{df_t_futures.shape}")
except Exception as e:
    print(f"错误：加载数据失败，请检查文件路径、Sheet名称和数据格式。错误信息：{e}")
    exit()

# --- 第二步：频率对齐与"未来收益矩阵"构建 ---

print("\n开始进行数据频率对齐，并构建未来收益矩阵...")

# 1. 将日频行情降采样为周频（取每周五收盘价）
df_t_weekly = df_t_futures[['收盘价']].resample('W-FRI').last()

# 2. 构建未来收益矩阵
max_lead_weeks = 12 # 设置最大领先期为12周
for k in range(1, max_lead_weeks + 1):
    # 计算未来k周的收益率
    df_t_weekly[f'Forward_{k}W_Return'] = df_t_weekly['收盘价'].shift(-k) / df_t_weekly['收盘价'] - 1
print(f"已成功计算从1周到{max_lead_weeks}周的未来收益率。")

# 3. 合并因子与收益率数据
# 使用merge_asof可以在日期不完全对齐时，找到最近的那个值，非常稳健
aligned_df = pd.merge_asof(df_t_weekly, df_factors, left_index=True, right_index=True, direction='nearest')
aligned_df.dropna(inplace=True) # 删除因计算未来收益率而在末尾产生的NaN行

print("因子与目标变量已对齐。")

# --- 第三步：为每个因子寻找其"最佳领先期" ---

print("\n开始为每个宏观因子寻找最佳领先期...")

# 获取因子列表
factor_list = df_factors.columns.tolist()

lead_time_results = []

for factor in factor_list:
    correlations = {}
    for k in range(1, max_lead_weeks + 1):
        target_col = f'Forward_{k}W_Return'
        # 计算斯皮尔曼秩相关系数 (Rank IC)
        # .corr()方法默认使用pearson，这里我们用scipy的spearmanr
        temp_df = aligned_df[[factor, target_col]].dropna()
        ic, _ = spearmanr(temp_df[factor], temp_df[target_col])
        correlations[k] = ic
    
    # 找到绝对值最大的相关系数对应的领先期
    best_k = max(correlations, key=lambda k: abs(correlations[k]))
    max_ic = correlations[best_k]
    
    lead_time_results.append({
        '因子名称': factor,
        '最佳领先期(周)': best_k,
        '最大RankIC': max_ic
    })

# 将结果转换为DataFrame
df_lead_time_analysis = pd.DataFrame(lead_time_results).set_index('因子名称')
print("各因子最佳领先期分析完成：")
print(df_lead_time_analysis)


# --- 第四步：构建基于"最佳领先期"的加权领先指数 ---

print("\n开始构建基于最佳领先期的加权领先指数...")

# 1. 构建时间对齐的"预测因子矩阵"
base_target_horizon = 2 # 我们的基准预测目标是未来2周
df_aligned_predictors = pd.DataFrame(index=aligned_df.index)

# 直接使用原始因子数据，不进行shift操作，因为我们已经在第一步找到了最佳领先期
for factor in factor_list:
    if factor in aligned_df.columns:
        df_aligned_predictors[factor] = aligned_df[factor]

# 2. 重新计算因子重要性（IC）和权重
target_col_name = f'Forward_{base_target_horizon}W_Return'
target_variable = aligned_df[target_col_name]

# 确保数据对齐
common_index = df_aligned_predictors.index.intersection(target_variable.index)
df_aligned_predictors_common = df_aligned_predictors.loc[common_index]
target_variable_common = target_variable.loc[common_index]

merged_for_weighting = pd.concat([df_aligned_predictors_common, target_variable_common], axis=1).dropna()
print(f"用于权重计算的数据形状：{merged_for_weighting.shape}")

weights_data = []
for factor in df_aligned_predictors.columns:
    if factor in merged_for_weighting.columns and len(merged_for_weighting) > 10:  # 确保有足够的数据
        try:
            ic, _ = spearmanr(merged_for_weighting[factor], merged_for_weighting[target_col_name])
            weights_data.append({'因子名称': factor, '对齐后IC': ic})
        except Exception as e:
            print(f"计算因子 {factor} 的IC时出错：{e}")
            weights_data.append({'因子名称': factor, '对齐后IC': 0.0})
    else:
        print(f"因子 {factor} 数据不足，设置IC为0")
        weights_data.append({'因子名称': factor, '对齐后IC': 0.0})

df_weights = pd.DataFrame(weights_data).set_index('因子名称')
df_weights['IC绝对值'] = df_weights['对齐后IC'].abs()
df_weights['最终权重'] = df_weights['IC绝对值'] / df_weights['IC绝对值'].sum()
print("\n因子最终权重计算完成：")
print(df_weights)


# 3. 因子预处理（方向统一 & 标准化）
# 方向统一
for factor in df_aligned_predictors.columns:
    ic_sign = np.sign(df_weights.loc[factor, '对齐后IC'])
    # 如果IC为负，则反转因子方向
    if ic_sign < 0:
        df_aligned_predictors[factor] = -df_aligned_predictors[factor]

# 滚动Z-Score标准化
rolling_window = 52
standardized_predictors = pd.DataFrame(index=df_aligned_predictors.index)
for col in df_aligned_predictors.columns:
    rolling_mean = df_aligned_predictors[col].rolling(window=rolling_window, min_periods=int(rolling_window/2)).mean()
    rolling_std = df_aligned_predictors[col].rolling(window=rolling_window, min_periods=int(rolling_window/2)).std()
    standardized_predictors[col + '_Z'] = (df_aligned_predictors[col] - rolling_mean) / (rolling_std + 1e-10)

standardized_predictors.fillna(method='bfill', inplace=True) # 修正后的填充逻辑


# 4. 最终指数合成
final_leading_index = pd.Series(0.0, index=standardized_predictors.index)
for factor in df_aligned_predictors.columns:
    weight = df_weights.loc[factor, '最终权重']
    final_leading_index += standardized_predictors[factor + '_Z'] * weight

index_results = pd.DataFrame({'加权领先指数': final_leading_index})
print("\n最终加权领先指数已合成！")


# --- 第五步：结果存储与可视化 ---

print("\n开始存储结果并进行可视化...")

# 5.1 存储到Excel文件
output_excel_path = os.path.join(output_dir, '国债期货因子分析与加权指数.xlsx')
with pd.ExcelWriter(output_excel_path) as writer:
    df_lead_time_analysis.to_excel(writer, sheet_name='因子领先期分析')
    df_weights.to_excel(writer, sheet_name='因子最终权重')
    
    # 准备最终对齐的数据表用于输出
    output_df = pd.concat([aligned_df[['收盘价', f'Forward_{base_target_horizon}W_Return']], index_results], axis=1)
    output_df.to_excel(writer, sheet_name='对齐数据与最终指数')

print(f"所有分析结果已成功保存至：{output_excel_path}")

# 5.2 可视化
output_plot_path = os.path.join(output_dir, '加权领先指数可视化图表.png')
plt.figure(figsize=(18, 8))
plt.plot(index_results.index, index_results['加权领先指数'], label='动态领先期加权指数', color='purple', linewidth=2)
# 动态计算阈值，例如取80%和20%分位数
upper_threshold = index_results['加权领先指数'].quantile(0.8)
lower_threshold = index_results['加权领先指数'].quantile(0.2)
plt.axhline(upper_threshold, color='gray', linestyle='--', linewidth=1, label=f'看多阈值 ({upper_threshold:.2f})')
plt.axhline(lower_threshold, color='gray', linestyle='-.', linewidth=1, label=f'风险警示 ({lower_threshold:.2f})')
plt.title('国债期货动态加权宏观领先指数', fontsize=16)
plt.legend()
plt.grid(True, linestyle='--', alpha=0.6)
plt.ylabel('指数值')
plt.xlabel('日期')

plt.tight_layout()
plt.savefig(output_plot_path, dpi=300, bbox_inches='tight')
print(f"可视化图表已成功保存至：{output_plot_path}")
plt.show()

print("\n代码执行完毕。")
