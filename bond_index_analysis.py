# ==============================================================================
# 10年国债期货（T）周度宏观领先指数合成代码 (纯粹领先版)
#
# 作者: cuiwei
# 日期: 2025-07-10
#
# 更新日志:
# - 【核心重构】完全剔除所有金融流动性因子，聚焦于构建纯粹的"领先"指数。
# - 【权重更新】采用新的两支柱权重体系，更突出战略性领先因子的作用。
# - 包含了季节性调整模块和修正后的滚动计算逻辑。
# ==============================================================================

# --- 第一步：环境准备与数据加载 ---

# 导入必要的Python库
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os # 导入os库以处理文件路径
from statsmodels.tsa.seasonal import seasonal_decompose # 导入季节性分解工具
import warnings
warnings.filterwarnings('ignore') # 忽略一些不影响结果的警告

# 设置matplotlib以支持中文显示（确保您的系统已安装中文字体，如SimHei）
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# --- 定义输入与输出路径 ---
# 输入文件路径
file_path = '/Users/<USER>/Desktop/国债期货量化因子.xlsx'
sheet_name = '周频因子'

# 输出文件夹路径（请确保该文件夹已创建）
output_dir = '/Users/<USER>/Desktop/国债期货量化结果/'
os.makedirs(output_dir, exist_ok=True) # 如果文件夹不存在，则自动创建

# 使用try-except结构以处理可能的文件不存在错误
try:
    # 加载数据，不设置索引列
    df = pd.read_excel(file_path, sheet_name=sheet_name)
    print('原始数据加载成功！')

    # 检查第一列是否包含日期数据
    first_col = df.columns[0]

    # 尝试将第一列转换为日期时间格式，跳过无法解析的行
    valid_dates = []
    valid_indices = []

    for i, value in enumerate(df[first_col]):
        try:
            # 尝试转换为日期时间
            date_val = pd.to_datetime(value)
            valid_dates.append(date_val)
            valid_indices.append(i)
        except:
            # 如果无法转换，跳过这一行
            print(f'跳过第{i+1}行，无法解析的值：{value}')
            continue

    # 只保留有效日期的行
    df = df.iloc[valid_indices].copy()
    df.index = valid_dates
    df = df.drop(columns=[first_col])  # 删除原来的日期列

    # 按日期排序，确保索引是单调的
    df = df.sort_index()

    print(f'清理后数据形状：{df.shape}')
    print("数据加载成功！")

except FileNotFoundError:
    print(f"错误：无法在路径 '{file_path}' 找到文件。请检查文件路径和名称是否正确。")
    exit()
except Exception as e:
    print(f'加载数据时出错：{e}')
    exit()

# --- 第二步：缺失值处理 ---

print('\n开始处理少量缺失值...')
# 对所有列的少量、零星缺失值进行前向填充，再用后向填充处理开头可能存在的缺失
df.fillna(method='ffill', inplace=True)
df.fillna(method='bfill', inplace=True)
print("缺失值已通过前向/后向填充处理完毕。")

# --- 第三步：因子季节性调整 ---

print('\n开始对具有强季节性的因子进行调整...')

# 创建一个新的DataFrame用于后续处理，以保持原始数据不变
processed_factors = df.copy()

# 定义具有明显季节性效应的因子列表（使用实际的列名）
seasonal_factors = [
    '高炉开工率(247家)', '北京:地铁客运量', '票房收入:电影',
    '30大中城市:成交面积:商品房', '唐山:价格:螺纹钢(HRB400,20mm)'
]

for col in seasonal_factors:
    if col in processed_factors.columns:
        decomposition = seasonal_decompose(processed_factors[col], model='additive', period=52)
        seasonally_adjusted = decomposition.trend + decomposition.resid
        processed_factors[col] = seasonally_adjusted
        print(f"因子 '{col}' 已完成季节性调整。")
    else:
        print(f"警告：在季节性调整列表中找不到因子 '{col}'，已跳过。")

processed_factors.fillna(method='ffill', inplace=True)
processed_factors.fillna(method='bfill', inplace=True)
print("季节性调整完成，并已处理因此产生的NaN值。")


# --- 第四步：因子计算与方向统一化 ---

print('\n开始进行因子计算与方向统一化处理...')

# 4.1 计算衍生的复合因子
try:
    # 先检查列名并打印出来
    print("当前数据列名：", list(processed_factors.columns))

    # 使用实际存在的列名
    processed_factors['信用利差'] = -(processed_factors['中债企业债到期收益率(AA):10年:周:平均值'] - processed_factors['中债国债到期收益率:10年:周:平均值'])
    processed_factors['股债ERP'] = -(1 / processed_factors['市盈率:沪深300指数:周:平均值'] - processed_factors['中债国债到期收益率:10年:周:平均值'] / 100)
    processed_factors['铜金比'] = -(processed_factors['加权平均价(主力合约):沪铜(9:00-15:00):周:平均值'] / processed_factors['加权平均价(主力合约):沪金(9:00-15:00):周:平均值'])
    print("信用利差, 股债ERP, 铜金比 计算完成。")
except KeyError as e:
    print(f"错误：计算衍生因子失败，找不到列：{e}。")
    # 尝试使用可能的列名
    try:
        if 'AA企业债-10Y国债收益率' in processed_factors.columns:
            processed_factors['信用利差'] = -processed_factors['AA企业债-10Y国债收益率']
            print("使用现有的信用利差列")
        if '股债ERP' in processed_factors.columns:
            processed_factors['股债ERP'] = processed_factors['股债ERP']  # 已存在，保持不变
            print("使用现有的股债ERP列")
        # 铜金比计算
        if '加权平均价(主力合约):沪铜(9:00-15:00):周:平均值' in processed_factors.columns and '加权平均价(主力合约):沪金(9:00-15:00):周:平均值' in processed_factors.columns:
            processed_factors['铜金比'] = -(processed_factors['加权平均价(主力合约):沪铜(9:00-15:00):周:平均值'] / processed_factors['加权平均价(主力合约):沪金(9:00-15:00):周:平均值'])
            print("铜金比计算完成")
        print("使用现有列完成衍生因子计算。")
    except Exception as e2:
        print(f"使用现有列也失败：{e2}")
        exit()

# 4.2 定义并批量反向处理因子（统一为数值越高，对T合约越【利好】）
# 【核心修改】此列表已剔除所有金融流动性因子，使用实际的列名
factors_to_invert = [
    '高炉开工率(247家)', '中国出口集装箱运价指数:综合指数', '北京:地铁客运量',
    '票房收入:电影', '30大中城市:成交面积:商品房',
    '南华工业品指数', '唐山:价格:螺纹钢(HRB400,20mm)',
    '波动率:50ETF期权', '恒生AH股溢价指数'
]

for col in factors_to_invert:
    if col in processed_factors.columns:
        processed_factors[col] = -processed_factors[col]
print("所有领先因子的方向已统一完毕。")

# --- 第五步：因子标准化 ---

print('\n开始对所有领先因子进行滚动Z-Score标准化...')
rolling_window = 52

# 定义所有需要进行标准化的最终因子列表（已不包含流动性因子）
final_factor_columns = factors_to_invert + ['信用利差', '股债ERP', '铜金比']
final_factor_columns = [col for col in final_factor_columns if col in processed_factors.columns]

standardized_factors = pd.DataFrame(index=processed_factors.index)

for col in final_factor_columns:
    rolling_mean = processed_factors[col].rolling(window=rolling_window, min_periods=int(rolling_window/2)).mean()
    rolling_std = processed_factors[col].rolling(window=rolling_window, min_periods=int(rolling_window/2)).std()
    standardized_factors[col + '_Z'] = (processed_factors[col] - rolling_mean) / (rolling_std + 1e-10)

# 【核心修正】仅使用后向填充(bfill)来处理滚动计算在数据序列【最开始】产生的NaN。
standardized_factors.fillna(method='bfill', inplace=True)
print(f"滚动 {rolling_window} 周的Z-Score标准化完成，并已修正填充逻辑。")

# --- 第六步：分项指数与最终指数合成 (核心重构) ---

print('\n开始合成两大领先分项指数和最终领先指数...')

# 6.1 定义两大领先分项指数的构成因子（使用实际的列名）
econ_inflation_factors = [
    '高炉开工率(247家)_Z', '中国出口集装箱运价指数:综合指数_Z', '北京:地铁客运量_Z',
    '票房收入:电影_Z', '30大中城市:成交面积:商品房_Z', '南华工业品指数_Z',
    '唐山:价格:螺纹钢(HRB400,20mm)_Z'
]
econ_inflation_factors = [f for f in econ_inflation_factors if f in standardized_factors.columns]

risk_appetite_factors = [
    '信用利差_Z', '股债ERP_Z', '铜金比_Z', '波动率:50ETF期权_Z', '恒生AH股溢价指数_Z'
]
risk_appetite_factors = [f for f in risk_appetite_factors if f in standardized_factors.columns]

# 6.2 创建一个DataFrame用于存放指数结果
index_results = pd.DataFrame(index=df.index)

# 使用等权重法计算各分项指数
index_results['实体与通胀领先指数'] = standardized_factors[econ_inflation_factors].mean(axis=1)
index_results['风险偏好领先指数'] = standardized_factors[risk_appetite_factors].mean(axis=1)
print("两大领先分项指数已通过等权重法合成。")

# 6.3 【核心修改】使用新的权重体系合成最终的纯粹领先指数
# 权重: 风险偏好(0.6) + 实体与通胀(0.4)，更突出战略领先因子的作用
weights = {'风险偏好领先指数': 0.6, '实体与通胀领先指数': 0.4}
index_results['最终领先指数'] = (index_results['风险偏好领先指数'] * weights['风险偏好领先指数'] +
                               index_results['实体与通胀领先指数'] * weights['实体与通胀领先指数'])
print("最终领先指数已通过新的专家权重法合成。")

# --- 第七步：结果存储与可视化 ---

print('\n开始存储结果并进行可视化...')

# 7.1 将所有结果合并到一个DataFrame中
final_results = pd.concat([df, processed_factors[final_factor_columns], standardized_factors, index_results], axis=1)

# 7.2 存储到指定的输出文件夹
output_excel_path = os.path.join(output_dir, '国债期货纯粹领先指数结果.xlsx')
output_plot_path = os.path.join(output_dir, '国债期货纯粹领先指数可视化图表.png')

try:
    final_results.to_excel(output_excel_path)
    print(f"所有结果已成功保存至：{output_excel_path}")
except Exception as e:
    print(f"错误：保存Excel文件失败。原因：{e}")

# 7.3 可视化最终领先指数和两个领先分项指数
plt.figure(figsize=(20, 12))

# 绘制最终领先指数
ax1 = plt.subplot(2, 1, 1)
try:
    plot_data = index_results.loc['2019-01-05':'2025-07-05']
except KeyError:
    # 如果指定的日期范围不存在，使用全部数据
    plot_data = index_results
ax1.plot(plot_data.index, plot_data['最终领先指数'], label='最终领先指数 (纯粹领先版)', color='red', linewidth=2)
ax1.axhline(0.20, color='gray', linestyle='--', linewidth=1, label='看多阈值 (+0.25)') # 阈值可根据回测调整
ax1.axhline(-0.20, color='gray', linestyle='-.', linewidth=1, label='风险警示 (-0.25)') # 阈值可根据回测调整
ax1.fill_between(plot_data.index, 0.20, plot_data['最终领先指数'], where=(plot_data['最终领先指数'] >= 0.20), color='mistyrose', interpolate=True)
ax1.fill_between(plot_data.index, -0.20, plot_data['最终领先指数'], where=(plot_data['最终领先指数'] <= -0.20), color='lightcyan', interpolate=True)
ax1.set_title('国债期货周度宏观领先指数 (纯粹领先版)', fontsize=16)
ax1.legend()
ax1.grid(True, linestyle='--', alpha=0.6)
ax1.set_ylabel('指数值')

# 绘制两个领先分项指数
ax2 = plt.subplot(2, 1, 2, sharex=ax1) # 共享X轴
ax2.plot(plot_data.index, plot_data['实体与通胀领先指数'], label='实体与通胀领先指数 (权重:0.8)', color='blue', alpha=0.8)
ax2.plot(plot_data.index, plot_data['风险偏好领先指数'], label='风险偏好领先指数 (权重:0.2)', color='purple', alpha=0.8)
ax2.set_title('两大领先分项指数走势', fontsize=16)
ax2.legend()
ax2.grid(True, linestyle='--', alpha=0.6)
ax2.set_ylabel('指数值 (等权重合成)')
ax2.set_xlabel('日期')

plt.tight_layout()

# 保存图表到指定文件夹
try:
    plt.savefig(output_plot_path, dpi=300, bbox_inches='tight')
    print(f"可视化图表已成功保存至：{output_plot_path}")
except Exception as e:
    print(f"错误：保存图表失败。原因：{e}")

# 显示图表
plt.show()

print("\n代码执行完毕。")
